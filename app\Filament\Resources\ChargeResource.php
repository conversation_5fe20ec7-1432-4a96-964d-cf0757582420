<?php

namespace App\Filament\Resources;

use App\Filament\Resources\ChargeResource\Pages;
use App\Filament\Resources\ChargeResource\RelationManagers;
use App\Models\Person;
use App\Models\Product;
use App\Models\Sale;
use Carbon\Carbon;
use Filament\Forms;
use Filament\Forms\Components\DatePicker;
use Filament\Forms\Components\Hidden;
use Filament\Forms\Components\Repeater;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\Textarea;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Components\Toggle;
use Filament\Forms\Form;
use Filament\Forms\Get;
use Filament\Forms\Set;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Actions\Action;
use Filament\Tables\Columns\Layout\Split;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Filters\Filter;
use Filament\Tables\Filters\Indicator;
use Filament\Tables\Filters\SelectFilter;
use Filament\Tables\Grouping\Group;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;
use Illuminate\Support\HtmlString;
use Filament\Support\Enums\MaxWidth;
use Leandrocfe\FilamentPtbrFormFields\Money;

class ChargeResource extends Resource
{
    protected static ?string $label = 'Cobrança';

    protected static ?string $model = Sale::class;

    protected static ?string $navigationIcon = 'phosphor-receipt';

    protected static ?int $navigationSort = 2;

    // public static function canCreate(): bool
    // {
    //     return false;
    // }

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Select::make('person_id')
                    ->label('Cliente')
                    ->options(Person::all()->pluck('name', 'id'))
                    ->searchable()
                    ->required(),

                Forms\Components\Select::make('product_id')
                    ->label('Produto')
                    ->options(Product::all()->pluck('name', 'id'))
                    ->searchable()
                    ->required()
                    ->reactive()
                    ->afterStateUpdated(fn($state, callable $set, callable $get) => self::updateUnitPriceAndTotal($get, $set)),

                Forms\Components\TextInput::make('quantity')
                    ->label('Quantidade')
                    ->required()
                    ->numeric()
                    ->default(1)
                    ->minValue(1)
                    ->reactive()
                    ->afterStateUpdated(fn($state, callable $set, callable $get) => self::updateUnitPriceAndTotal($get, $set)),

                Forms\Components\DateTimePicker::make('sale_date')
                    ->label('Data da Venda')
                    ->default(now())
                    ->native(false)
                    ->displayFormat('d/m/Y'),

                Forms\Components\Toggle::make('paid')
                    ->label('Pago?')
                    ->default(false)
                    ->reactive()
                    ->afterStateUpdated(function ($state, callable $set) {
                        if ($state) {
                            $set('payment_date', now());
                        } else {
                            $set('payment_date', null);
                        }
                    }),

                Forms\Components\DateTimePicker::make('payment_date')
                    ->label('Data do Pagamento')
                    ->native(false)
                    ->displayFormat('d/m/Y')
                    ->after('sale_date')
                    ->visible(fn($get) => $get('paid') === true),

                Forms\Components\Textarea::make('observations')
                    ->label('Observações')
                    ->columnSpanFull(),

                Hidden::make('unit_price')->default(0),
                Hidden::make('total')->default(0),
            ]);
    }

    private static function updateUnitPriceAndTotal(Get $get, Set $set): void
    {
        $product = Product::find($get('product_id'));
        if ($product) {
            $set('unit_price', $product->price);
            $set('total', $product->price * $get('quantity'));
        } else {
            $set('unit_price', 0);
            $set('total', 0);
        }
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([

                Tables\Columns\TextColumn::make('person.name')
                    ->label('Cliente')
                    ->searchable()
                    ->sortable()
                    ->formatStateUsing(function ($state, $record) {
                        return new HtmlString("
                                <div>
                                    <div><strong>{$record->person->name}</strong></div>
                                    <div class='text-sm text-gray-500'>{$record->person->department->name}</div>
                                </div>
                            ");
                    }),

                Tables\Columns\TextColumn::make('quantity')
                    ->label('Quantidade')
                    ->numeric()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),

                Tables\Columns\TextColumn::make('total')
                    ->label('Total')
                    ->sortable()
                    ->money('brl', 0, 'pt-BR')
                    ->toggleable(isToggledHiddenByDefault: false),

                Tables\Columns\IconColumn::make('has_charge_requests')
                    ->label('Cobrança enviada')
                    ->boolean()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),

            ])
            ->filters([
                SelectFilter::make('person_id')
                    ->label('Cliente')
                    ->relationship('person', 'name')
                    ->searchable()
                    ->preload()
                    ->multiple(),

                Filter::make('sale_period')
                    ->form([
                        Select::make('sale_month')
                            ->label('Mês da venda')
                            ->options([
                                1 => 'Janeiro',
                                2 => 'Fevereiro',
                                3 => 'Março',
                                4 => 'Abril',
                                5 => 'Maio',
                                6 => 'Junho',
                                7 => 'Julho',
                                8 => 'Agosto',
                                9 => 'Setembro',
                                10 => 'Outubro',
                                11 => 'Novembro',
                                12 => 'Dezembro',
                            ])
                            ->placeholder('Selecione o mês da venda')
                            ->default((Carbon::now())->subMonth()->month)
                            ->required()
                            ->selectablePlaceholder(false),

                        Select::make('sale_year')
                            ->label('Ano da venda')
                            ->options(self::getFilterYears())
                            ->placeholder('Selecione o ano da venda')
                            ->default((Carbon::now())->subMonth()->year)
                            ->required()
                            ->selectablePlaceholder(false),

                        Toggle::make('doesntHaveChargeRequests')
                            ->label('Cobrança não enviada'),

                    ])
                    ->query(
                        fn(Builder $query, array $data): Builder =>
                        $query
                            ->when(
                                $data['sale_month'],
                                fn(Builder $query, $value): Builder => $query->whereMonth('sale_date', $value),
                            )
                            ->when(
                                $data['sale_year'],
                                fn(Builder $query, $value): Builder => $query->whereYear('sale_date', $value),
                            )
                            ->when(
                                $data['doesntHaveChargeRequests'],
                                fn(Builder $query): Builder => $query->whereDoesntHave(
                                    'person.chargeRequests',
                                    fn(Builder $query): Builder =>
                                    $query->where('month', $data['sale_month'])->where('year', $data['sale_year'])
                                )
                            )
                    )
                    ->indicateUsing(function (array $data): array {
                        if ($data['sale_month'] && $data['sale_year']) {
                            $monthName = Carbon::create()->month((int)$data['sale_month'])->translatedFormat('F');
                            $year = $data['sale_year'];

                            $isPast = Carbon::createFromDate($year, $data['sale_month'], 1)->lastOfMonth()->isPast();

                            $color = $isPast ? 'primary' : 'danger';
                            return [
                                Indicator::make("Vendas de $monthName de $year")->color($color)->removable(false),
                            ];
                        }

                        return [];
                    }),

            ])
            ->actions([
                Action::make('whatsapp')
                    ->label('Enviar')
                    ->url(fn(Sale $record, $livewire): string => self::getNotificationUrl($record, $livewire), shouldOpenInNewTab: true)
                    ->openUrlInNewTab(),

                Action::make('aprovar')
                    ->label('Confirmar Pagamento')
                    ->icon('heroicon-o-check-circle')
                    ->color('success')
                    ->requiresConfirmation()
                    ->modalHeading('Confirmação de Pagamento')
                    ->modalDescription('Por favor, confirme o status do pagamento recebido. Essa ação é importante para manter o controle financeiro atualizado corretamente.')
                    ->modalSubmitActionLabel('Pagamento Completo')
                    ->modalCancelActionLabel('Cancelar')
                    ->modalWidth(MaxWidth::ThreeExtraLarge)
                    ->extraModalFooterActions(fn(): array => [
                        Action::make('rejeitar')
                            ->label('Pagamento Parcial ')
                            ->color('si')
                            ->icon('heroicon-o-x-circle')
                            ->form([
                                Repeater::make('members')
                                    ->label('Remova os items não pagos')
                                    ->schema([
                                        TextInput::make('name')->label('Nome')->default('Produto 1')->disabled(true)->columnSpan(2),
                                        TextInput::make('quantity')->label('Quantidade')->default(1)->disabled(true)->columnSpan(1),
                                        Money::make('total')->label('Total')->default(15.5)->disabled(true)->columnSpan(1)
                                    ])
                                    ->columns(4)
                                    ->defaultItems(3)
                                    ->reorderable(false)
                                    ->addable(false),
                            ])
                            ->modalCancelActionLabel('Cancelar')
                            ->modalSubmitActionLabel('Confirmar ')
                            ->modalWidth(MaxWidth::FiveExtraLarge)
                            ->action(function (array $data, $record) {
                                $record->status = 'rejeitado';
                                $record->motivo = $data['motivo'] ?? '';
                                $record->save();
                            })
                            ->cancelParentActions()
                    ])
                    ->action(function ($record) {
                        dd('completo');
                        // Aqui vai a lógica ao confirmar a aprovação
                        $record->status = 'aprovado';
                        $record->save();
                    }),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function getNotificationUrl(Sale $record, $livewire): string
    {
        return route('admin.send-charge-message', [
            'personId' => $record->person_id,
            'month' => $livewire->tableFilters['sale_period']['sale_month'],
            'year' => $livewire->tableFilters['sale_period']['sale_year'],
        ]);
    }

    public static function getFilterYears(): array
    {
        $first = Sale::orderBy('sale_date', 'asc')->first();

        $years = [];

        $year = Carbon::create($first->sale_date)->year;
        while ($year <= date('Y')) {
            $years[$year] = $year;
            $year++;
        }

        return $years;
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListCharges::route('/'),
            // 'create' => Pages\CreateCharge::route('/create'),
            // 'edit' => Pages\EditCharge::route('/{record}/edit'),
        ];
    }
}
