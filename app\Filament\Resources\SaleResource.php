<?php

namespace App\Filament\Resources;

use App\Filament\Resources\SaleResource\Pages;
use App\Filament\Resources\SaleResource\RelationManagers;
use App\Models\Person;
use App\Models\Product;
use App\Models\Sale;
use Carbon\Carbon;
use Filament\Forms;
use Filament\Forms\Components\DatePicker;
use Filament\Forms\Components\Hidden;
use Filament\Forms\Components\Select;
use Filament\Forms\Form;
use Filament\Forms\Get;
use Filament\Forms\Set;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Actions\Action;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Filters\Filter;
use Filament\Tables\Filters\Indicator;
use Filament\Tables\Filters\SelectFilter;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;
use Illuminate\Support\HtmlString;

class SaleResource extends Resource
{
    protected static ?string $label = 'Vendas';

    protected static ?string $model = Sale::class;

    protected static ?string $navigationIcon = 'phosphor-shopping-cart';

    protected static ?int $navigationSort = 1;

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Select::make('person_id')
                    ->label('Cliente')
                    ->options(Person::all()->pluck('name', 'id'))
                    ->searchable()
                    ->required(),

                Forms\Components\Select::make('product_id')
                    ->label('Produto')
                    ->options(Product::all()->pluck('name', 'id'))
                    ->searchable()
                    ->required()
                    ->reactive()
                    ->afterStateUpdated(fn($state, callable $set, callable $get) => self::updateUnitPriceAndTotal($get, $set)),

                Forms\Components\TextInput::make('quantity')
                    ->label('Quantidade')
                    ->required()
                    ->numeric()
                    ->default(1)
                    ->minValue(1)
                    ->reactive()
                    ->afterStateUpdated(fn($state, callable $set, callable $get) => self::updateUnitPriceAndTotal($get, $set)),

                Forms\Components\DateTimePicker::make('sale_date')
                    ->label('Data da Venda')
                    ->default(now())
                    ->native(false)
                    ->displayFormat('d/m/Y'),

                Forms\Components\Toggle::make('paid')
                    ->label('Pago?')
                    ->default(false)
                    ->reactive()
                    ->afterStateUpdated(function ($state, callable $set) {
                        if ($state) {
                            $set('payment_date', now());
                        } else {
                            $set('payment_date', null);
                        }
                    }),

                Forms\Components\DateTimePicker::make('payment_date')
                    ->label('Data do Pagamento')
                    ->native(false)
                    ->displayFormat('d/m/Y')
                    ->after('sale_date')
                    ->visible(fn($get) => $get('paid') === true),

                Forms\Components\Textarea::make('observations')
                    ->label('Observações')
                    ->columnSpanFull(),

                Hidden::make('unit_price')->default(0),
                Hidden::make('total')->default(0),
            ]);
    }

    private static function updateUnitPriceAndTotal(Get $get, Set $set): void
    {
        $product = Product::find($get('product_id'));
        if ($product) {
            $set('unit_price', $product->price);
            $set('total', $product->price * $get('quantity'));
        } else {
            $set('unit_price', 0);
            $set('total', 0);
        }
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('person.name')
                    ->label('Cliente')
                    ->searchable()
                    ->sortable()
                    ->formatStateUsing(function ($state, $record) {
                        return new HtmlString("
                                <div>
                                    <div><strong>{$record->person->name}</strong></div>
                                    <div class='text-sm text-gray-500'>{$record->person->department->name}</div>
                                </div>
                            ");
                    }),

                Tables\Columns\TextColumn::make('product.name')
                    ->label('Produto')
                    ->searchable()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),

                Tables\Columns\TextColumn::make('quantity')
                    ->label('Quantidade')
                    ->numeric()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),

                Tables\Columns\TextColumn::make('total')
                    ->label('Total')
                    ->sortable()
                    ->money('brl', 0, 'pt-BR')
                    ->toggleable(isToggledHiddenByDefault: false),

                Tables\Columns\TextColumn::make('sale_date')
                    ->label('Data da Venda')
                    ->dateTime('d/m/Y')
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: false),

                Tables\Columns\IconColumn::make('paid')
                    ->label('Pago?')
                    ->boolean()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: false),

                Tables\Columns\TextColumn::make('payment_date')
                    ->label('Data do Pagamento')
                    ->dateTime('d/m/Y')
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: false),

                Tables\Columns\TextColumn::make('observations')
                    ->label('Observações')
                    ->searchable()
                    ->limit(12)
                    ->tooltip(function (TextColumn $column): ?string {
                        $state = $column->getState();

                        if (strlen($state) <= $column->getCharacterLimit()) {
                            return null;
                        }

                        return $state;
                    })
                    ->toggleable(isToggledHiddenByDefault: true),

                Tables\Columns\TextColumn::make('created_at')
                    ->label('Criado em')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true)
                    ->dateTime('d/m/Y'),

                Tables\Columns\TextColumn::make('updated_at')
                    ->label('Atualizado em')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true)
                    ->since()
                    ->dateTimeTooltip(),
            ])
            ->filters([
                Filter::make('is_paid')
                    ->label('Pagos')
                    ->query(fn(Builder $query): Builder => $query->where('paid', true))
                    ->toggle(),

                Filter::make('is_not_paid')
                    ->label('Não pagos')
                    ->query(fn(Builder $query): Builder => $query->where('paid', false))
                    ->toggle(),

                SelectFilter::make('person_id')
                    ->label('Cliente')
                    ->relationship('person', 'name')
                    ->searchable()
                    ->preload()
                    ->multiple(),

                Filter::make('sale_month')
                    ->form([
                        Select::make('sale_month')
                            ->label('Mês da venda')
                            ->options([
                                1 => 'Janeiro',
                                2 => 'Fevereiro',
                                3 => 'Março',
                                4 => 'Abril',
                                5 => 'Maio',
                                6 => 'Junho',
                                7 => 'Julho',
                                8 => 'Agosto',
                                9 => 'Setembro',
                                10 => 'Outubro',
                                11 => 'Novembro',
                                12 => 'Dezembro',
                            ])
                            ->placeholder('Selecione o mês da venda'),
                    ])
                    ->query(function ($query, array $data) {
                        if ($data['sale_month'] ?? null) {
                            $query->whereMonth('sale_date', $data['sale_month']);
                        }
                    })
                    ->indicateUsing(function (array $data): array {
                        if ($data['sale_month'] ?? null) {
                            $monthName = Carbon::create()->month((int)$data['sale_month'])->translatedFormat('F');
                            return [
                                Indicator::make("Vendas de $monthName")
                                    ->removeField('sale_month'),
                            ];
                        }

                        return [];
                    }),

                Filter::make('sale_date')
                    ->label('Data da Venda')
                    ->form([
                        DatePicker::make('sale_from')->label('Venda a partir de'),
                        DatePicker::make('sale_until')->label('Venda até'),
                    ])
                    ->query(function (Builder $query, array $data): Builder {
                        return $query
                            ->when(
                                $data['sale_from'],
                                fn(Builder $query, $date): Builder => $query->whereDate('sale_date', '>=', $date),
                            )
                            ->when(
                                $data['sale_until'],
                                fn(Builder $query, $date): Builder => $query->whereDate('sale_date', '<=', $date),
                            );
                    })
                    ->indicateUsing(function (array $data): array {
                        $indicators = [];

                        if ($data['sale_from'] ?? null) {
                            $indicators[] = Indicator::make('Venda a partir de ' . Carbon::parse($data['sale_from'])->translatedFormat("j \\de F"))
                                ->removeField('sale_from');
                        }

                        if ($data['sale_until'] ?? null) {
                            $indicators[] = Indicator::make('Venda até ' . Carbon::parse($data['sale_until'])->translatedFormat("j \\de F"))
                                ->removeField('sale_until');
                        }

                        return $indicators;
                    })
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListSales::route('/'),
            // 'create' => Pages\CreateSale::route('/create'),
            // 'edit' => Pages\EditSale::route('/{record}/edit'),
        ];
    }
}
